use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// 后台管理用户模型
#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
pub struct MySqlAdminUser {
    /// 用户ID
    pub id: u64,

    /// 账号
    pub account: Option<String>,

    /// 密码（应该存储哈希值而非明文）
    pub password: Option<String>,

    /// 创建时间
    pub created_at: Option<DateTime<Utc>>,

    /// 更新时间
    pub updated_at: Option<DateTime<Utc>>,
}

impl MySqlAdminUser {
    /// 创建新的后台管理用户
    pub fn new(account: String, password: String) -> Self {
        let now = Utc::now();
        Self {
            id: 0, // 数据库会自动分配ID
            account: Some(account),
            password: Some(password),
            created_at: Some(now),
            updated_at: Some(now),
        }
    }

    /// 获取表名
    pub fn table_name() -> &'static str {
        "admin_users"
    }
}
