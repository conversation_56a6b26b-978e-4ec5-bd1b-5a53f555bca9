// 导出MySQL模型模块
mod user;
mod favorite;
mod bookmark;
mod app_version;
mod tag;
mod bookmark_tag;
mod collection;
mod note;
mod note_draft;
mod material;
mod augment_user;
mod tencent_asr_task;
mod task;
mod prompt;
mod system_prompt;
mod ai_usage;
mod admin_user;

pub use user::MySqlUser;
pub use favorite::{MySqlFavorite, new_favorite_order};
pub use bookmark::MySqlBookmark;
pub use app_version::MySqlAppVersion;
pub use tag::{MySqlTag, TagWithCount};
pub use bookmark_tag::{MySqlBookmarkTag, BookmarkTagDetail};
pub use collection::MySqlCollection;
pub use note::MySqlNote;
pub use note_draft::MySqlNoteDraft;
pub use material::{MySqlMaterial, MaterialType, MaterialStatus};
pub use augment_user::MySqlAugmentUser;
pub use tencent_asr_task::{MySqlTencentAsrTask, AsrTaskStatus};
pub use task::{MySqlTask, TaskType, TaskStatus};
pub use prompt::MySqlPrompt;
pub use system_prompt::MySqlSystemPrompt;
pub use ai_usage::MySqlAiUsage;
pub use admin_user::MySqlAdminUser;
