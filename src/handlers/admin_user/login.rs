use actix_web::{web, HttpResponse, Responder};
use log::{info, error, debug};
use uuid::Uuid;
use std::time::{SystemTime, UNIX_EPOCH};
use redis::RedisError;

use crate::AppState;
use crate::dto::{ApiResponse, ErrorCode, admin_user::{AdminLoginRequest, AdminLoginData}};
use crate::services::mysql::MySqlAdminUserServiceError;

/**
 * 后台管理用户登录处理函数
 * 
 * 请求参数：
 * {
 *   "account": "管理员账号",
 *   "password": "管理员密码"
 * }
 * 
 * 返回数据：
 * {
 *   "code": 0,
 *   "message": "成功",
 *   "data": {
 *     "user_id": "用户ID",
 *     "account": "管理员账号",
 *     "token": "认证令牌"
 *   }
 * }
 *
 * @param data 应用状态数据
 * @param user_data 后台管理用户登录请求数据
 * @return HTTP 响应
 */
pub async fn handle(data: web::Data<AppState>, user_data: web::Json<AdminLoginRequest>) -> impl Responder {
    // 校验参数
    if user_data.account.trim().is_empty() {
        let error_code = ErrorCode::ValidationError;
        return HttpResponse::BadRequest().json(ApiResponse {
            code: error_code.code(),
            data: serde_json::Value::Null,
            message: "账号不能为空".to_string(),
        });
    }

    if user_data.password.trim().is_empty() {
        let error_code = ErrorCode::ValidationError;
        return HttpResponse::BadRequest().json(ApiResponse {
            code: error_code.code(),
            data: serde_json::Value::Null,
            message: "密码不能为空".to_string(),
        });
    }

    // 获取后台管理用户服务
    let admin_user_service = match &data.admin_user_service {
        Some(service) => service,
        None => {
            let error_code = ErrorCode::ServiceUnavailable;
            return HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                data: serde_json::Value::Null,
                message: "后台管理用户服务不可用".to_string(),
            });
        }
    };

    // 验证用户登录
    match admin_user_service.verify_login(&user_data.account, &user_data.password).await {
        Ok(user) => {
            let user_id = user.id.to_string();
            info!("后台管理用户登录成功: {}", user_id);

            // 生成 token
            let token = generate_token();
            debug!("生成 token: {}", token);

            // 存储 token 到 Redis
            if let Err(e) = store_token_in_redis(&data, &token, &user_id).await {
                error!("存储 token 到 Redis 失败: {}", e);
                let error_code = ErrorCode::SystemError;
                return HttpResponse::InternalServerError().json(ApiResponse {
                    code: error_code.code(),
                    data: serde_json::Value::Null,
                    message: "登录失败，请稍后重试".to_string(),
                });
            }

            let success_code = ErrorCode::Success;
            HttpResponse::Ok().json(ApiResponse {
                code: success_code.code(),
                data: AdminLoginData {
                    user_id,
                    account: user.account.unwrap_or_default(),
                    token,
                },
                message: success_code.message().to_string(),
            })
        },
        Err(e) => {
            error!("后台管理用户登录失败: {}", e);
            // 根据错误类型设置不同的错误代码
            let error_code = match e {
                MySqlAdminUserServiceError::UserNotFound => ErrorCode::UserNotFound,
                MySqlAdminUserServiceError::InvalidPassword => ErrorCode::PasswordMismatch,
                MySqlAdminUserServiceError::DatabaseError(_) => ErrorCode::DatabaseError,
                _ => ErrorCode::SystemError,
            };

            HttpResponse::BadRequest().json(ApiResponse {
                code: error_code.code(),
                data: serde_json::Value::Null,
                message: error_code.message().to_string(),
            })
        }
    }
}

/// 生成唯一的 token
fn generate_token() -> String {
    // 使用 UUID v4 生成随机 token
    let uuid = Uuid::new_v4();
    // 添加时间戳以确保唯一性
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs();

    format!("admin-{}-{}", uuid, timestamp)
}

/// 将 token 存储到 Redis 中
async fn store_token_in_redis(app_state: &web::Data<AppState>, token: &str, user_id: &str) -> Result<(), RedisError> {
    // 获取 Redis 客户端
    if let Some(db) = &app_state.db {
        // 设置 token 到 Redis，有效期为 7 天（604800 秒）
        // 使用不同的前缀区分后台管理用户token
        let redis_key = format!("admin_token:{}", token);
        db.redis.set_ex(&redis_key, user_id, 604800)
    } else {
        Err(RedisError::from((redis::ErrorKind::IoError, "Redis connection not available")))
    }
}
