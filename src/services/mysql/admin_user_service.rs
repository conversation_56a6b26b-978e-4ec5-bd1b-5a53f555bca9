use std::sync::Arc;
use sqlx::{MySql, Pool};
use log::{info, error};
use thiserror::Error;

use crate::models::mysql::MySqlAdminUser;

/// MySQL后台管理用户服务错误
#[derive(Debug, Error)]
pub enum MySqlAdminUserServiceError {
    #[error("数据库错误: {0}")]
    DatabaseError(#[from] sqlx::Error),

    #[error("用户不存在")]
    UserNotFound,

    #[error("用户已存在")]
    UserAlreadyExists,

    #[error("密码错误")]
    InvalidPassword,
}

/// MySQL后台管理用户服务
pub struct MySqlAdminUserService {
    pool: Arc<Pool<MySql>>,
}

impl MySqlAdminUserService {
    /// 创建新的MySQL后台管理用户服务实例
    pub fn new(pool: Pool<MySql>) -> Self {
        Self {
            pool: Arc::new(pool),
        }
    }

    /// 创建新用户
    pub async fn create_user(&self, account: String, password: String) -> Result<MySqlAdminUser, MySqlAdminUserServiceError> {
        // 检查用户是否已存在
        let existing_user = sqlx::query_as::<_, MySqlAdminUser>(
            "SELECT * FROM admin_users WHERE account = ?"
        )
        .bind(&account)
        .fetch_optional(&*self.pool)
        .await?;

        if existing_user.is_some() {
            return Err(MySqlAdminUserServiceError::UserAlreadyExists);
        }

        // 插入用户到数据库
        let user_id = sqlx::query(
            "INSERT INTO admin_users (account, password) VALUES (?, ?)"
        )
        .bind(&account)
        .bind(&password)
        .execute(&*self.pool)
        .await?
        .last_insert_id();

        // 获取新创建的用户
        let user = sqlx::query_as::<_, MySqlAdminUser>(
            "SELECT * FROM admin_users WHERE id = ?"
        )
        .bind(user_id)
        .fetch_one(&*self.pool)
        .await?;

        Ok(user)
    }

    /// 验证登录
    pub async fn verify_login(&self, account: &str, password: &str) -> Result<MySqlAdminUser, MySqlAdminUserServiceError> {
        // 查找用户
        let user = sqlx::query_as::<_, MySqlAdminUser>(
            "SELECT * FROM admin_users WHERE account = ?"
        )
        .bind(account)
        .fetch_optional(&*self.pool)
        .await?
        .ok_or(MySqlAdminUserServiceError::UserNotFound)?;

        // 验证密码
        if let Some(user_password) = &user.password {
            if user_password != password {
                return Err(MySqlAdminUserServiceError::InvalidPassword);
            }
        } else {
            return Err(MySqlAdminUserServiceError::InvalidPassword);
        }

        Ok(user)
    }

    /// 根据ID查找用户
    pub async fn find_by_id(&self, id: u64) -> Result<Option<MySqlAdminUser>, MySqlAdminUserServiceError> {
        let user = sqlx::query_as::<_, MySqlAdminUser>(
            "SELECT * FROM admin_users WHERE id = ?"
        )
        .bind(id)
        .fetch_optional(&*self.pool)
        .await?;

        Ok(user)
    }

    /// 根据账号查找用户
    pub async fn find_by_account(&self, account: &str) -> Result<Option<MySqlAdminUser>, MySqlAdminUserServiceError> {
        let user = sqlx::query_as::<_, MySqlAdminUser>(
            "SELECT * FROM admin_users WHERE account = ?"
        )
        .bind(account)
        .fetch_optional(&*self.pool)
        .await?;

        Ok(user)
    }
}
