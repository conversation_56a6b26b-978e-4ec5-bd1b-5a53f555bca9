#!/bin/bash

# 测试后台管理用户登录接口脚本
# 使用方法: ./scripts/test_admin_login.sh [server_url]
# server_url: 服务器地址 (默认: http://localhost:8080)

# 设置脚本在遇到错误时退出
set -e

# 获取服务器地址参数，默认为 localhost:8080
SERVER_URL=${1:-http://localhost:8080}

echo "测试后台管理用户登录接口..."
echo "服务器地址: $SERVER_URL"
echo ""

# 测试登录接口
echo "1. 测试登录接口 POST /admin/users/login"
echo "请求数据: {\"account\": \"admin\", \"password\": \"123456\"}"

RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
  -X POST \
  -H "Content-Type: application/json" \
  -d '{"account": "admin", "password": "123456"}' \
  "$SERVER_URL/admin/users/login")

# 分离响应体和状态码
HTTP_CODE=$(echo "$RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
RESPONSE_BODY=$(echo "$RESPONSE" | sed '/HTTP_CODE:/d')

echo "HTTP状态码: $HTTP_CODE"
echo "响应内容:"
echo "$RESPONSE_BODY" | jq . 2>/dev/null || echo "$RESPONSE_BODY"
echo ""

# 检查响应状态
if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ 登录成功测试通过"
    
    # 尝试解析token
    TOKEN=$(echo "$RESPONSE_BODY" | jq -r '.data.token' 2>/dev/null || echo "")
    if [ "$TOKEN" != "" ] && [ "$TOKEN" != "null" ]; then
        echo "✅ Token获取成功: ${TOKEN:0:20}..."
        
        # 测试使用token访问需要认证的接口
        echo ""
        echo "2. 测试使用token访问需要认证的接口"
        echo "请求头: Authorization: Bearer $TOKEN"
        
        AUTH_RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
          -H "Authorization: Bearer $TOKEN" \
          "$SERVER_URL/test/ping")
        
        AUTH_HTTP_CODE=$(echo "$AUTH_RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
        AUTH_RESPONSE_BODY=$(echo "$AUTH_RESPONSE" | sed '/HTTP_CODE:/d')
        
        echo "HTTP状态码: $AUTH_HTTP_CODE"
        echo "响应内容:"
        echo "$AUTH_RESPONSE_BODY" | jq . 2>/dev/null || echo "$AUTH_RESPONSE_BODY"
        
        if [ "$AUTH_HTTP_CODE" = "200" ]; then
            echo "✅ Token认证测试通过"
        else
            echo "❌ Token认证测试失败"
        fi
    else
        echo "❌ Token获取失败"
    fi
elif [ "$HTTP_CODE" = "400" ]; then
    echo "⚠️  登录失败 - 可能是用户不存在或密码错误"
    echo "请先创建后台管理用户或检查账号密码"
else
    echo "❌ 登录测试失败"
fi

echo ""
echo "3. 测试错误情况 - 空账号"
ERROR_RESPONSE=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
  -X POST \
  -H "Content-Type: application/json" \
  -d '{"account": "", "password": "123456"}' \
  "$SERVER_URL/admin/users/login")

ERROR_HTTP_CODE=$(echo "$ERROR_RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
ERROR_RESPONSE_BODY=$(echo "$ERROR_RESPONSE" | sed '/HTTP_CODE:/d')

echo "HTTP状态码: $ERROR_HTTP_CODE"
echo "响应内容:"
echo "$ERROR_RESPONSE_BODY" | jq . 2>/dev/null || echo "$ERROR_RESPONSE_BODY"

if [ "$ERROR_HTTP_CODE" = "400" ]; then
    echo "✅ 参数验证测试通过"
else
    echo "❌ 参数验证测试失败"
fi

echo ""
echo "🎉 后台管理用户登录接口测试完成！"
echo ""
echo "如果登录失败，请确保："
echo "1. 服务器正在运行在 $SERVER_URL"
echo "2. 数据库中存在后台管理用户数据"
echo "3. 可以使用以下SQL创建测试用户："
echo "   INSERT INTO admin_users (account, password) VALUES ('admin', '123456');"
