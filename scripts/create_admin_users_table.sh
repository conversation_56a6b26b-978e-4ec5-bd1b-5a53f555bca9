#!/bin/bash

# 创建后台管理用户表脚本
# 使用方法: ./scripts/create_admin_users_table.sh [environment]
# environment: development, staging, production (默认: development)

# 设置脚本在遇到错误时退出
set -e

# 获取环境参数，默认为 development
ENVIRONMENT=${1:-development}

# 根据环境设置数据库连接参数
case $ENVIRONMENT in
    "development")
        DB_HOST=${MYSQL_HOST:-localhost}
        DB_PORT=${MYSQL_PORT:-3306}
        DB_USER=${MYSQL_USERNAME:-root}
        DB_PASSWORD=${MYSQL_PASSWORD:-123456}
        DB_NAME=${MYSQL_DATABASE:-aishoucang}
        ;;
    "staging")
        DB_HOST=${MYSQL_HOST:-localhost}
        DB_PORT=${MYSQL_PORT:-3306}
        DB_USER=${MYSQL_USERNAME:-root}
        DB_PASSWORD=${MYSQL_PASSWORD}
        DB_NAME=${MYSQL_DATABASE:-aishoucang_staging}
        ;;
    "production")
        DB_HOST=${MYSQL_HOST}
        DB_PORT=${MYSQL_PORT:-3306}
        DB_USER=${MYSQL_USERNAME}
        DB_PASSWORD=${MYSQL_PASSWORD}
        DB_NAME=${MYSQL_DATABASE}
        ;;
    *)
        echo "错误: 不支持的环境 '$ENVIRONMENT'"
        echo "支持的环境: development, staging, production"
        exit 1
        ;;
esac

# 检查必需的环境变量
if [ -z "$DB_PASSWORD" ]; then
    echo "错误: 数据库密码未设置"
    echo "请设置 MYSQL_PASSWORD 环境变量"
    exit 1
fi

echo "正在为 $ENVIRONMENT 环境创建后台管理用户表..."
echo "数据库: $DB_HOST:$DB_PORT/$DB_NAME"

# 创建表的SQL语句
SQL="
CREATE TABLE IF NOT EXISTS admin_users (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    account VARCHAR(100) NULL COMMENT '账号',
    password VARCHAR(255) NULL COMMENT '密码（哈希值）',
    created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY idx_account (account)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
"

# 执行SQL
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "$SQL"

if [ $? -eq 0 ]; then
    echo "✅ 后台管理用户表创建成功"
    
    # 检查表是否存在
    TABLE_EXISTS=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "SHOW TABLES LIKE 'admin_users';" | wc -l)
    
    if [ $TABLE_EXISTS -gt 1 ]; then
        echo "✅ 表验证成功: admin_users 表已存在"
        
        # 显示表结构
        echo ""
        echo "表结构:"
        mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "DESCRIBE admin_users;"
    else
        echo "❌ 表验证失败: admin_users 表不存在"
        exit 1
    fi
else
    echo "❌ 后台管理用户表创建失败"
    exit 1
fi

echo ""
echo "🎉 后台管理用户表创建完成！"
echo ""
echo "接下来可以："
echo "1. 使用应用的注册功能创建后台管理用户"
echo "2. 或者直接在数据库中插入用户数据"
echo ""
echo "示例插入语句:"
echo "INSERT INTO admin_users (account, password) VALUES ('admin', 'your_hashed_password');"
